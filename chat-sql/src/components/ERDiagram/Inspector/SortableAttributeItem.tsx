// 可拖拽属性项组件
import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  Box,
  IconButton,
  Chip,
  TextField,
  MenuItem,
  Menu,
  Tooltip,
  Typography,
} from "@mui/material";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  DragIndicator as DragIndicatorIcon,
} from "@mui/icons-material";
import { ERAttribute } from "@/types/ERDiagramTypes/erDiagram";
import {
  dataTypeParamConfig,
  dataTypeOptions,
  parseDataType,
  buildDataType,
} from "@/types/ERDiagramTypes/dataTypes";

interface SortableAttributeItemProps {
  id: string;
  attribute: ERAttribute;
  entityId: string;
  isEditing: boolean;
  editingName: string;
  isComposing: boolean;
  menuAnchor: HTMLElement | null;
  attributeParams: string[];
  onNameChange: (value: string) => void;
  onNameSave: () => void;
  onCompositionStart: () => void;
  onCompositionEnd: () => void;
  onMenuOpen: (event: React.MouseEvent<HTMLElement>) => void;
  onMenuClose: () => void;
  onDeleteAttribute: () => void;
  onTypeChange: (newType: string) => void;
  onParamChange: (paramIndex: number, value: string) => void;
}

const SortableAttributeItem: React.FC<SortableAttributeItemProps> = ({
  id,
  attribute,
  entityId,
  isEditing,
  editingName,
  isComposing,
  menuAnchor,
  attributeParams,
  onNameChange,
  onNameSave,
  onCompositionStart,
  onCompositionEnd,
  onMenuOpen,
  onMenuClose,
  onDeleteAttribute,
  onTypeChange,
  onParamChange,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.6 : 1,
  };

  const parsedType = parseDataType(attribute.dataType || "");
  const config = dataTypeParamConfig[parsedType.typeName];

  return (
    <Box
      ref={setNodeRef}
      style={style}
      sx={{
        display: "flex",
        alignItems: "center",
        gap: 1,
        p: 1,
        border: "1px solid",
        borderColor: "divider",
        borderRadius: 1,
        backgroundColor: isDragging ? "action.hover" : "background.paper",
        "&:hover": {
          backgroundColor: "action.hover",
        },
      }}
    >
      {/* 拖拽手柄 */}
      <Tooltip title='拖拽排序'>
        <IconButton
          size='small'
          sx={{
            cursor: "grab",
            color: "var(--secondary-text)",
            "&:active": {
              cursor: "grabbing",
            },
          }}
          {...attributes}
          {...listeners}
        >
          <DragIndicatorIcon fontSize='small' />
        </IconButton>
      </Tooltip>

      {/* 属性名称 */}
      <Box sx={{ flex: 1, minWidth: 0 }}>
        {isEditing ? (
          <TextField
            size='small'
            value={editingName}
            onChange={(e) => onNameChange(e.target.value)}
            onBlur={onNameSave}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !isComposing) {
                onNameSave();
              }
            }}
            onCompositionStart={onCompositionStart}
            onCompositionEnd={onCompositionEnd}
            autoFocus
            sx={{ width: "100%" }}
          />
        ) : (
          <Typography
            variant='body2'
            sx={{
              fontWeight: attribute.isPrimaryKey ? "bold" : "normal",
              color: attribute.isPrimaryKey
                ? "primary.main"
                : "var(--secondary-text)",
              cursor: "pointer",
            }}
            onDoubleClick={() => onNameChange(attribute.name)}
          >
            {attribute.name}
          </Typography>
        )}
      </Box>

      {/* 数据类型 */}
      <Box sx={{ minWidth: 120 }}>
        <TextField
          select
          size='small'
          value={parsedType.typeName}
          onChange={(e) => onTypeChange(e.target.value)}
          sx={{ minWidth: 100 }}
        >
          {dataTypeOptions.map((option) => (
            <MenuItem key={option} value={option}>
              {option}
            </MenuItem>
          ))}
        </TextField>
      </Box>

      {/* 参数输入 */}
      {config && config.paramCount > 0 && (
        <Box sx={{ display: "flex", gap: 0.5 }}>
          {config.paramLabels.map((label, index) => (
            <TextField
              key={label}
              size='small'
              type='number'
              placeholder={label}
              value={attributeParams[index] || ""}
              onChange={(e) => onParamChange(index, e.target.value)}
              sx={{ width: 60 }}
              inputProps={{
                min: config.validation?.[index]?.min,
                max: config.validation?.[index]?.max,
              }}
            />
          ))}
        </Box>
      )}

      {/* 标签 */}
      <Box sx={{ display: "flex", gap: 0.5 }}>
        {attribute.isPrimaryKey && (
          <Chip label='PK' size='small' color='primary' variant='outlined' />
        )}
        {attribute.isRequired && (
          <Chip
            label='必需'
            size='small'
            color='secondary'
            variant='outlined'
          />
        )}
      </Box>

      {/* 操作菜单 */}
      <Tooltip title='更多操作'>
        <IconButton
          size='small'
          onClick={onMenuOpen}
          sx={{ color: "var(--secondary-text)" }}
        >
          <MoreVertIcon fontSize='small' />
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={onMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem onClick={() => onNameChange(attribute.name)}>
          <EditIcon fontSize='small' sx={{ mr: 1 }} />
          编辑名称
        </MenuItem>
        <MenuItem onClick={onDeleteAttribute} sx={{ color: "error.main" }}>
          <DeleteIcon fontSize='small' sx={{ mr: 1 }} />
          删除属性
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default SortableAttributeItem;
